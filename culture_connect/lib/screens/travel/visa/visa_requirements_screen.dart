// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/services/travel/visa/travel_buddy_api_service.dart';
import 'package:culture_connect/widgets/travel/visa/visa_requirement_card.dart';
import 'package:culture_connect/widgets/travel/visa/country_selector_widget.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for checking visa requirements between countries
class VisaRequirementsScreen extends ConsumerStatefulWidget {
  /// Creates a new visa requirements screen
  const VisaRequirementsScreen({
    super.key,
    this.prefilledPassportCountry,
    this.prefilledDestinationCountry,
  });

  /// Pre-filled passport country (from user profile or previous selection)
  final String? prefilledPassportCountry;

  /// Pre-filled destination country (from flight booking)
  final String? prefilledDestinationCountry;

  @override
  ConsumerState<VisaRequirementsScreen> createState() =>
      _VisaRequirementsScreenState();
}

class _VisaRequirementsScreenState
    extends ConsumerState<VisaRequirementsScreen> {
  // State variables
  String? _selectedPassportCountry;
  String? _selectedDestinationCountry;
  VisaRequirement? _visaRequirement;
  bool _isLoading = false;
  String? _errorMessage;

  // Services
  final TravelBuddyApiService _travelBuddyService = TravelBuddyApiService();

  @override
  void initState() {
    super.initState();
    _selectedPassportCountry = widget.prefilledPassportCountry;
    _selectedDestinationCountry = widget.prefilledDestinationCountry;

    // Auto-search if both countries are pre-filled
    if (_selectedPassportCountry != null &&
        _selectedDestinationCountry != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _searchVisaRequirements();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Visa Requirements'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            onPressed: _showHelpDialog,
            icon: const Icon(Icons.help_outline),
            tooltip: 'Help',
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          const SizedBox(height: 24),
          _buildCountrySelectors(theme),
          const SizedBox(height: 24),
          _buildSearchButton(theme),
          const SizedBox(height: 32),
          _buildResults(theme),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Check Visa Requirements',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Get up-to-date visa information for your travel destination. Select your passport country and destination to see requirements.',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurface.withAlpha(179),
          ),
        ),
      ],
    );
  }

  Widget _buildCountrySelectors(ThemeData theme) {
    return Column(
      children: [
        CountrySelectorWidget(
          label: 'Your Passport Country',
          selectedCountry: _selectedPassportCountry,
          onCountrySelected: (country) {
            setState(() {
              _selectedPassportCountry = country;
              _clearResults();
            });
          },
          icon: Icons.person,
        ),
        const SizedBox(height: 16),
        CountrySelectorWidget(
          label: 'Destination Country',
          selectedCountry: _selectedDestinationCountry,
          onCountrySelected: (country) {
            setState(() {
              _selectedDestinationCountry = country;
              _clearResults();
            });
          },
          icon: Icons.flight_takeoff,
        ),
      ],
    );
  }

  Widget _buildSearchButton(ThemeData theme) {
    final canSearch = _selectedPassportCountry != null &&
        _selectedDestinationCountry != null &&
        _selectedPassportCountry != _selectedDestinationCountry;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: canSearch && !_isLoading ? _searchVisaRequirements : null,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.search),
                  SizedBox(width: 8),
                  Text(
                    'Check Visa Requirements',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildResults(ThemeData theme) {
    if (_isLoading) {
      return _buildLoadingState(theme);
    }

    if (_errorMessage != null) {
      return ErrorView(
        error: _errorMessage!,
        onRetry: _searchVisaRequirements,
      );
    }

    if (_visaRequirement != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Visa Requirements',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          VisaRequirementCard(
            visaRequirement: _visaRequirement!,
            onActionPressed: _handleVisaAction,
          ),
          const SizedBox(height: 24),
          _buildAdditionalInfo(theme),
        ],
      );
    }

    return _buildEmptyState(theme);
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Checking visa requirements...',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.travel_explore,
            size: 64,
            color: theme.colorScheme.primary.withAlpha(128),
          ),
          const SizedBox(height: 16),
          Text(
            'Select Countries to Check Requirements',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose your passport country and destination to see visa requirements and travel information.',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Important Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• Visa requirements can change frequently. Always verify with official sources before travel.\n'
              '• Processing times may vary based on your location and current demand.\n'
              '• Some countries may have additional entry requirements beyond visas.\n'
              '• Consider applying for your visa well in advance of your travel date.',
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _showVisaServiceProviders,
                    icon: const Icon(Icons.business),
                    label: const Text('Find Visa Services'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _addToTravelPlanner,
                    icon: const Icon(Icons.bookmark_add),
                    label: const Text('Save to Planner'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Action methods
  Future<void> _searchVisaRequirements() async {
    if (_selectedPassportCountry == null ||
        _selectedDestinationCountry == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _visaRequirement = null;
    });

    try {
      final requirement = await _travelBuddyService.getVisaRequirements(
        passportCountry: _selectedPassportCountry!,
        destinationCountry: _selectedDestinationCountry!,
      );

      if (mounted) {
        setState(() {
          _visaRequirement = requirement;
          _isLoading = false;
          if (requirement == null) {
            _errorMessage =
                'No visa information found for this country combination. Please check with the embassy or consulate.';
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage =
              'Failed to fetch visa requirements. Please try again.';
          _isLoading = false;
        });
      }
    }
  }

  void _handleVisaAction() {
    if (_visaRequirement == null) return;

    switch (_visaRequirement!.requirementType) {
      case VisaRequirementType.noVisaRequired:
      case VisaRequirementType.visaExemption:
        _showNoVisaRequiredDialog();
        break;
      case VisaRequirementType.visaRequired:
      case VisaRequirementType.eVisa:
      case VisaRequirementType.visaOnArrival:
      case VisaRequirementType.specialPermit:
        _showVisaServiceProviders();
        break;
    }
  }

  void _showVisaServiceProviders() {
    if (_selectedDestinationCountry == null) return;

    // TODO: Navigate to visa service provider marketplace
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Visa service provider marketplace coming soon!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _addToTravelPlanner() {
    if (_visaRequirement == null) return;

    // TODO: Integrate with travel planner/itinerary service
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Visa requirement saved to your travel planner!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Visa Requirements Help'),
        content: const SingleChildScrollView(
          child: Text(
            'This tool helps you check visa requirements for international travel.\n\n'
            'How to use:\n'
            '1. Select your passport country (nationality)\n'
            '2. Select your destination country\n'
            '3. Tap "Check Visa Requirements" to get information\n\n'
            'Important notes:\n'
            '• Information is provided by Travel Buddy API\n'
            '• Requirements can change frequently\n'
            '• Always verify with official embassy sources\n'
            '• Apply for visas well in advance\n'
            '• Some countries may have additional entry requirements\n\n'
            'For assistance with visa applications, use our visa service provider marketplace.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showNoVisaRequiredDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 24,
            ),
            SizedBox(width: 8),
            Text('Great News!'),
          ],
        ),
        content: Text(
          'No visa is required for travel from $_selectedPassportCountry to $_selectedDestinationCountry. '
          'You can travel freely, but make sure your passport is valid and check for any other entry requirements.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Understood'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _addToTravelPlanner();
            },
            child: const Text('Save to Planner'),
          ),
        ],
      ),
    );
  }

  void _clearResults() {
    setState(() {
      _visaRequirement = null;
      _errorMessage = null;
    });
  }

  @override
  void dispose() {
    _travelBuddyService.dispose();
    super.dispose();
  }
}
