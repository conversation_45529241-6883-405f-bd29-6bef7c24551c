// Flutter imports
import 'dart:async';

// Package imports

// Project imports
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/notification_service.dart';

/// Enum for visa tracking status
enum VisaTrackingStatus {
  active,
  expiringSoon,
  expired,
  overstay,
}

/// Extension for visa tracking status display
extension VisaTrackingStatusExtension on VisaTrackingStatus {
  /// Display name for the status
  String get displayName {
    switch (this) {
      case VisaTrackingStatus.active:
        return 'Active';
      case VisaTrackingStatus.expiringSoon:
        return 'Expiring Soon';
      case VisaTrackingStatus.expired:
        return 'Expired';
      case VisaTrackingStatus.overstay:
        return 'Overstay';
    }
  }

  /// Color for the status
  String get colorHex {
    switch (this) {
      case VisaTrackingStatus.active:
        return '#10B981'; // Green
      case VisaTrackingStatus.expiringSoon:
        return '#F59E0B'; // Amber
      case VisaTrackingStatus.expired:
        return '#EF4444'; // Red
      case VisaTrackingStatus.overstay:
        return '#DC2626'; // Dark Red
    }
  }

  /// Icon name for the status
  String get iconName {
    switch (this) {
      case VisaTrackingStatus.active:
        return 'check_circle';
      case VisaTrackingStatus.expiringSoon:
        return 'warning';
      case VisaTrackingStatus.expired:
        return 'error';
      case VisaTrackingStatus.overstay:
        return 'dangerous';
    }
  }
}

/// Model representing a tracked visa
class TrackedVisa {
  /// Unique identifier for the tracked visa
  final String id;

  /// Country the visa is for
  final String country;

  /// Visa type
  final String visaType;

  /// Entry date
  final DateTime entryDate;

  /// Visa expiry date
  final DateTime expiryDate;

  /// Maximum stay duration in days
  final int maxStayDays;

  /// Current tracking status
  final VisaTrackingStatus status;

  /// Whether notifications are enabled
  final bool notificationsEnabled;

  /// Custom notification preferences
  final List<int> notificationDays;

  /// Additional notes
  final String? notes;

  /// Creates a new tracked visa
  const TrackedVisa({
    required this.id,
    required this.country,
    required this.visaType,
    required this.entryDate,
    required this.expiryDate,
    required this.maxStayDays,
    required this.status,
    required this.notificationsEnabled,
    required this.notificationDays,
    this.notes,
  });

  /// Creates a copy with updated fields
  TrackedVisa copyWith({
    String? id,
    String? country,
    String? visaType,
    DateTime? entryDate,
    DateTime? expiryDate,
    int? maxStayDays,
    VisaTrackingStatus? status,
    bool? notificationsEnabled,
    List<int>? notificationDays,
    String? notes,
  }) {
    return TrackedVisa(
      id: id ?? this.id,
      country: country ?? this.country,
      visaType: visaType ?? this.visaType,
      entryDate: entryDate ?? this.entryDate,
      expiryDate: expiryDate ?? this.expiryDate,
      maxStayDays: maxStayDays ?? this.maxStayDays,
      status: status ?? this.status,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      notificationDays: notificationDays ?? this.notificationDays,
      notes: notes ?? this.notes,
    );
  }

  /// Days remaining until expiry
  int get daysUntilExpiry {
    final now = DateTime.now();
    return expiryDate.difference(now).inDays;
  }

  /// Days remaining in allowed stay
  int get daysRemainingInStay {
    final now = DateTime.now();
    final maxStayDate = entryDate.add(Duration(days: maxStayDays));
    return maxStayDate.difference(now).inDays;
  }

  /// Days since entry
  int get daysSinceEntry {
    final now = DateTime.now();
    return now.difference(entryDate).inDays;
  }

  /// Whether the visa is expired
  bool get isExpired {
    return DateTime.now().isAfter(expiryDate);
  }

  /// Whether the stay is overstay
  bool get isOverstay {
    final maxStayDate = entryDate.add(Duration(days: maxStayDays));
    return DateTime.now().isAfter(maxStayDate);
  }

  /// Whether the visa is expiring soon (within notification threshold)
  bool get isExpiringSoon {
    if (notificationDays.isEmpty) return false;
    final minNotificationDays =
        notificationDays.reduce((a, b) => a < b ? a : b);
    return daysUntilExpiry <= minNotificationDays && daysUntilExpiry > 0;
  }
}

/// Service for smart visa expiration tracking
class VisaTrackingService {
  /// Singleton instance
  static final VisaTrackingService _instance = VisaTrackingService._internal();

  /// Factory constructor
  factory VisaTrackingService() => _instance;

  /// Internal constructor
  VisaTrackingService._internal();

  // Dependencies
  final LoggingService _loggingService = LoggingService();
  final NotificationService _notificationService = NotificationService();

  // State
  bool _isInitialized = false;
  final List<TrackedVisa> _trackedVisas = [];
  Timer? _trackingTimer;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize dependencies
      await _notificationService.initialize();

      // Load cached tracked visas
      await _loadCachedTrackedVisas();

      // Start periodic tracking updates
      _startPeriodicTracking();

      _isInitialized = true;
      _loggingService.info(
          'VisaTrackingService', 'Service initialized successfully');
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaTrackingService',
        'Error initializing service',
        e,
        stackTrace,
      );
      _isInitialized = true; // Continue with empty state
    }
  }

  /// Add a visa for tracking
  Future<TrackedVisa> addTrackedVisa({
    required String country,
    required String visaType,
    required DateTime entryDate,
    required DateTime expiryDate,
    required int maxStayDays,
    bool notificationsEnabled = true,
    List<int>? customNotificationDays,
    String? notes,
  }) async {
    await initialize();

    final visaId = '${country}_${entryDate.millisecondsSinceEpoch}';
    final notificationDays =
        customNotificationDays ?? _getDefaultNotificationDays(maxStayDays);

    final trackedVisa = TrackedVisa(
      id: visaId,
      country: country,
      visaType: visaType,
      entryDate: entryDate,
      expiryDate: expiryDate,
      maxStayDays: maxStayDays,
      status: _calculateStatus(
          entryDate, expiryDate, maxStayDays, notificationDays),
      notificationsEnabled: notificationsEnabled,
      notificationDays: notificationDays,
      notes: notes,
    );

    _trackedVisas.add(trackedVisa);

    // Schedule notifications if enabled
    if (notificationsEnabled) {
      await _scheduleNotifications(trackedVisa);
    }

    // Cache the tracked visa
    await _cacheTrackedVisa(trackedVisa);

    _loggingService.info(
      'VisaTrackingService',
      'Added tracked visa: $visaId for $country',
    );

    return trackedVisa;
  }

  /// Get all tracked visas
  List<TrackedVisa> getAllTrackedVisas() {
    return List.unmodifiable(_trackedVisas);
  }

  /// Get tracked visas by status
  List<TrackedVisa> getTrackedVisasByStatus(VisaTrackingStatus status) {
    return _trackedVisas.where((visa) => visa.status == status).toList();
  }

  /// Get active tracked visas
  List<TrackedVisa> getActiveTrackedVisas() {
    return _trackedVisas
        .where((visa) =>
            visa.status == VisaTrackingStatus.active ||
            visa.status == VisaTrackingStatus.expiringSoon)
        .toList();
  }

  /// Update notification preferences for a tracked visa
  Future<TrackedVisa> updateNotificationPreferences({
    required String visaId,
    required bool notificationsEnabled,
    List<int>? notificationDays,
  }) async {
    await initialize();

    final index = _trackedVisas.indexWhere((visa) => visa.id == visaId);
    if (index == -1) {
      throw Exception('Tracked visa not found: $visaId');
    }

    final updatedVisa = _trackedVisas[index].copyWith(
      notificationsEnabled: notificationsEnabled,
      notificationDays:
          notificationDays ?? _trackedVisas[index].notificationDays,
    );

    _trackedVisas[index] = updatedVisa;

    // Reschedule notifications
    await _cancelNotifications(visaId);
    if (notificationsEnabled) {
      await _scheduleNotifications(updatedVisa);
    }

    // Update cache
    await _cacheTrackedVisa(updatedVisa);

    _loggingService.info(
      'VisaTrackingService',
      'Updated notification preferences for visa: $visaId',
    );

    return updatedVisa;
  }

  /// Remove a tracked visa
  Future<bool> removeTrackedVisa(String visaId) async {
    await initialize();

    final initialLength = _trackedVisas.length;
    _trackedVisas.removeWhere((visa) => visa.id == visaId);
    final removed = _trackedVisas.length < initialLength;

    if (removed) {
      // Cancel notifications
      await _cancelNotifications(visaId);

      // Remove from cache
      await _removeCachedTrackedVisa(visaId);

      _loggingService.info(
        'VisaTrackingService',
        'Removed tracked visa: $visaId',
      );
    }

    return removed;
  }

  /// Get default notification days based on stay duration
  List<int> _getDefaultNotificationDays(int maxStayDays) {
    if (maxStayDays <= 7) {
      return [3, 1]; // 3 days, 1 day
    } else if (maxStayDays <= 30) {
      return [15, 7, 3, 1]; // 15, 7, 3, 1 days
    } else if (maxStayDays <= 90) {
      return [30, 15, 7, 3, 1]; // 30, 15, 7, 3, 1 days
    } else {
      return [60, 30, 15, 7, 3, 1]; // 60, 30, 15, 7, 3, 1 days
    }
  }

  /// Calculate visa tracking status
  VisaTrackingStatus _calculateStatus(
    DateTime entryDate,
    DateTime expiryDate,
    int maxStayDays,
    List<int> notificationDays,
  ) {
    final now = DateTime.now();
    final maxStayDate = entryDate.add(Duration(days: maxStayDays));

    // Check for overstay
    if (now.isAfter(maxStayDate)) {
      return VisaTrackingStatus.overstay;
    }

    // Check for expired visa
    if (now.isAfter(expiryDate)) {
      return VisaTrackingStatus.expired;
    }

    // Check for expiring soon
    final daysUntilExpiry = expiryDate.difference(now).inDays;
    final daysUntilMaxStay = maxStayDate.difference(now).inDays;
    final minDays =
        daysUntilExpiry < daysUntilMaxStay ? daysUntilExpiry : daysUntilMaxStay;

    if (notificationDays.isNotEmpty) {
      final minNotificationDays =
          notificationDays.reduce((a, b) => a < b ? a : b);
      if (minDays <= minNotificationDays && minDays > 0) {
        return VisaTrackingStatus.expiringSoon;
      }
    }

    return VisaTrackingStatus.active;
  }

  /// Schedule notifications for a tracked visa
  Future<void> _scheduleNotifications(TrackedVisa visa) async {
    // Implementation for scheduling notifications
    _loggingService.info(
      'VisaTrackingService',
      'Scheduling notifications for visa: ${visa.id}',
    );
  }

  /// Cancel notifications for a visa
  Future<void> _cancelNotifications(String visaId) async {
    // Implementation for canceling notifications
    _loggingService.info(
      'VisaTrackingService',
      'Canceling notifications for visa: $visaId',
    );
  }

  /// Start periodic tracking updates
  void _startPeriodicTracking() {
    _trackingTimer = Timer.periodic(const Duration(hours: 6), (timer) {
      _updateAllVisaStatuses();
    });
  }

  /// Update all visa statuses
  void _updateAllVisaStatuses() {
    for (int i = 0; i < _trackedVisas.length; i++) {
      final visa = _trackedVisas[i];
      final newStatus = _calculateStatus(
        visa.entryDate,
        visa.expiryDate,
        visa.maxStayDays,
        visa.notificationDays,
      );

      if (newStatus != visa.status) {
        _trackedVisas[i] = visa.copyWith(status: newStatus);
        _cacheTrackedVisa(_trackedVisas[i]);
      }
    }
  }

  /// Load cached tracked visas
  Future<void> _loadCachedTrackedVisas() async {
    // Implementation for loading cached tracked visas
    _loggingService.info(
      'VisaTrackingService',
      'Loading cached tracked visas - placeholder implementation',
    );
  }

  /// Cache a tracked visa
  Future<void> _cacheTrackedVisa(TrackedVisa visa) async {
    // Implementation for caching tracked visas
    _loggingService.info(
      'VisaTrackingService',
      'Caching tracked visa: ${visa.id}',
    );
  }

  /// Remove cached tracked visa
  Future<void> _removeCachedTrackedVisa(String visaId) async {
    // Implementation for removing cached tracked visas
    _loggingService.info(
      'VisaTrackingService',
      'Removing cached tracked visa: $visaId',
    );
  }

  /// Dispose the service
  void dispose() {
    _trackingTimer?.cancel();
  }
}
