// Flutter imports
import 'dart:io';

// Package imports

// Project imports
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/services/travel/visa/visa_routing_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/storage_service.dart';

/// Enum for document upload status
enum DocumentUploadStatus {
  pending,
  uploading,
  uploaded,
  failed,
  processing,
  approved,
  rejected,
}

/// Extension for document upload status display
extension DocumentUploadStatusExtension on DocumentUploadStatus {
  /// Display name for the status
  String get displayName {
    switch (this) {
      case DocumentUploadStatus.pending:
        return 'Pending Upload';
      case DocumentUploadStatus.uploading:
        return 'Uploading';
      case DocumentUploadStatus.uploaded:
        return 'Uploaded';
      case DocumentUploadStatus.failed:
        return 'Upload Failed';
      case DocumentUploadStatus.processing:
        return 'Processing';
      case DocumentUploadStatus.approved:
        return 'Approved';
      case DocumentUploadStatus.rejected:
        return 'Rejected';
    }
  }

  /// Color for the status
  String get colorHex {
    switch (this) {
      case DocumentUploadStatus.pending:
        return '#6B7280'; // Gray
      case DocumentUploadStatus.uploading:
        return '#3B82F6'; // Blue
      case DocumentUploadStatus.uploaded:
        return '#10B981'; // Green
      case DocumentUploadStatus.failed:
        return '#EF4444'; // Red
      case DocumentUploadStatus.processing:
        return '#F59E0B'; // Amber
      case DocumentUploadStatus.approved:
        return '#059669'; // Emerald
      case DocumentUploadStatus.rejected:
        return '#DC2626'; // Red
    }
  }

  /// Icon name for the status
  String get iconName {
    switch (this) {
      case DocumentUploadStatus.pending:
        return 'schedule';
      case DocumentUploadStatus.uploading:
        return 'cloud_upload';
      case DocumentUploadStatus.uploaded:
        return 'cloud_done';
      case DocumentUploadStatus.failed:
        return 'error';
      case DocumentUploadStatus.processing:
        return 'hourglass_empty';
      case DocumentUploadStatus.approved:
        return 'check_circle';
      case DocumentUploadStatus.rejected:
        return 'cancel';
    }
  }
}

/// Model representing a visa document
class VisaDocument {
  /// Unique identifier for the document
  final String id;

  /// Document name/title
  final String name;

  /// Document type (passport, photo, bank_statement, etc.)
  final String type;

  /// File path or URL
  final String filePath;

  /// Upload status
  final DocumentUploadStatus status;

  /// Upload progress (0.0 to 1.0)
  final double uploadProgress;

  /// File size in bytes
  final int fileSize;

  /// MIME type
  final String mimeType;

  /// Upload timestamp
  final DateTime uploadedAt;

  /// Provider feedback/notes
  final String? providerNotes;

  /// Whether this document is required
  final bool isRequired;

  /// Creates a new visa document
  const VisaDocument({
    required this.id,
    required this.name,
    required this.type,
    required this.filePath,
    required this.status,
    required this.uploadProgress,
    required this.fileSize,
    required this.mimeType,
    required this.uploadedAt,
    this.providerNotes,
    required this.isRequired,
  });

  /// Creates a copy with updated fields
  VisaDocument copyWith({
    String? id,
    String? name,
    String? type,
    String? filePath,
    DocumentUploadStatus? status,
    double? uploadProgress,
    int? fileSize,
    String? mimeType,
    DateTime? uploadedAt,
    String? providerNotes,
    bool? isRequired,
  }) {
    return VisaDocument(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      filePath: filePath ?? this.filePath,
      status: status ?? this.status,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      providerNotes: providerNotes ?? this.providerNotes,
      isRequired: isRequired ?? this.isRequired,
    );
  }

  /// Formatted file size
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}

/// Service for managing visa documents conditionally based on service type
class VisaDocumentManagerService {
  /// Singleton instance
  static final VisaDocumentManagerService _instance =
      VisaDocumentManagerService._internal();

  /// Factory constructor
  factory VisaDocumentManagerService() => _instance;

  /// Internal constructor
  VisaDocumentManagerService._internal();

  // Dependencies
  final LoggingService _loggingService = LoggingService();
  final StorageService _storageService =
      StorageService(loggingService: LoggingService());

  // State
  bool _isInitialized = false;
  final Map<String, List<VisaDocument>> _applicationDocuments = {};

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load cached documents
      await _loadCachedDocuments();

      _isInitialized = true;
      _loggingService.info(
          'VisaDocumentManagerService', 'Service initialized successfully');
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaDocumentManagerService',
        'Error initializing service',
        e,
        stackTrace,
      );
      _isInitialized = true; // Continue with empty state
    }
  }

  /// Check if document management is required for a service route
  bool isDocumentManagementRequired(VisaServiceRoute route) {
    switch (route) {
      case VisaServiceRoute.selfService:
        return false; // No document management for self-service
      case VisaServiceRoute.hybrid:
        return true; // Optional document management
      case VisaServiceRoute.providerAssisted:
      case VisaServiceRoute.providerRequired:
        return true; // Required document management
    }
  }

  /// Get required documents for a visa application
  List<String> getRequiredDocuments(VisaRequirement visaRequirement) {
    final baseDocuments = visaRequirement.requiredDocuments;
    final additionalDocuments = <String>[];

    // Add common documents based on visa type
    switch (visaRequirement.requirementType) {
      case VisaRequirementType.visaRequired:
        additionalDocuments.addAll([
          'Passport Photo',
          'Bank Statement',
          'Travel Itinerary',
        ]);
        break;
      case VisaRequirementType.eVisa:
        additionalDocuments.addAll([
          'Passport Photo',
          'Accommodation Proof',
        ]);
        break;
      case VisaRequirementType.visaOnArrival:
        additionalDocuments.addAll([
          'Return Ticket',
          'Hotel Booking',
        ]);
        break;
      case VisaRequirementType.specialPermit:
        additionalDocuments.addAll([
          'Invitation Letter',
          'Sponsor Documents',
          'Purpose Statement',
        ]);
        break;
      default:
        break;
    }

    return [...baseDocuments, ...additionalDocuments];
  }

  /// Upload a document for a visa application
  Future<VisaDocument> uploadDocument({
    required String applicationId,
    required String documentType,
    required File file,
    required bool isRequired,
    Function(double)? onProgress,
  }) async {
    await initialize();

    final documentId =
        '${applicationId}_${documentType}_${DateTime.now().millisecondsSinceEpoch}';

    // Create initial document entry
    final document = VisaDocument(
      id: documentId,
      name: file.path.split('/').last,
      type: documentType,
      filePath: file.path,
      status: DocumentUploadStatus.uploading,
      uploadProgress: 0.0,
      fileSize: await file.length(),
      mimeType: _getMimeType(file.path),
      uploadedAt: DateTime.now(),
      isRequired: isRequired,
    );

    // Add to application documents
    _applicationDocuments[applicationId] ??= [];
    _applicationDocuments[applicationId]!.add(document);

    try {
      // Upload to storage
      final uploadUrl = await _storageService.uploadFile(
        file: file,
        path: 'visa_documents/$applicationId',
        onProgress: (bytesTransferred, totalBytes) {
          final progress = bytesTransferred / totalBytes;
          onProgress?.call(progress);

          // Update document progress
          final index = _applicationDocuments[applicationId]!
              .indexWhere((d) => d.id == documentId);
          if (index != -1) {
            _applicationDocuments[applicationId]![index] = document.copyWith(
              uploadProgress: progress,
            );
          }
        },
      );

      if (uploadUrl != null) {
        // Update document with success status
        final updatedDocument = document.copyWith(
          filePath: uploadUrl,
          status: DocumentUploadStatus.uploaded,
          uploadProgress: 1.0,
        );

        final index = _applicationDocuments[applicationId]!
            .indexWhere((d) => d.id == documentId);
        if (index != -1) {
          _applicationDocuments[applicationId]![index] = updatedDocument;
        }

        // Cache the document
        await _cacheDocument(applicationId, updatedDocument);

        _loggingService.info(
          'VisaDocumentManagerService',
          'Document uploaded successfully: $documentId',
        );

        return updatedDocument;
      } else {
        throw Exception('Failed to upload document to storage');
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaDocumentManagerService',
        'Error uploading document',
        e,
        stackTrace,
      );

      // Update document with failed status
      final failedDocument = document.copyWith(
        status: DocumentUploadStatus.failed,
      );

      final index = _applicationDocuments[applicationId]!
          .indexWhere((d) => d.id == documentId);
      if (index != -1) {
        _applicationDocuments[applicationId]![index] = failedDocument;
      }

      return failedDocument;
    }
  }

  /// Get documents for a visa application
  List<VisaDocument> getApplicationDocuments(String applicationId) {
    return _applicationDocuments[applicationId] ?? [];
  }

  /// Delete a document
  Future<bool> deleteDocument(String applicationId, String documentId) async {
    await initialize();

    try {
      final documents = _applicationDocuments[applicationId];
      if (documents != null) {
        documents.removeWhere((doc) => doc.id == documentId);

        // Remove from cache
        await _removeCachedDocument(applicationId, documentId);

        _loggingService.info(
          'VisaDocumentManagerService',
          'Document deleted: $documentId',
        );

        return true;
      }
      return false;
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaDocumentManagerService',
        'Error deleting document',
        e,
        stackTrace,
      );
      return false;
    }
  }

  /// Get MIME type from file extension
  String _getMimeType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'pdf':
        return 'application/pdf';
      default:
        return 'application/octet-stream';
    }
  }

  /// Load cached documents
  Future<void> _loadCachedDocuments() async {
    // Implementation for loading cached documents
    _loggingService.info(
      'VisaDocumentManagerService',
      'Loading cached documents - placeholder implementation',
    );
  }

  /// Cache a document
  Future<void> _cacheDocument(
      String applicationId, VisaDocument document) async {
    // Implementation for caching documents
    _loggingService.info(
      'VisaDocumentManagerService',
      'Caching document: ${document.id}',
    );
  }

  /// Remove cached document
  Future<void> _removeCachedDocument(
      String applicationId, String documentId) async {
    // Implementation for removing cached documents
    _loggingService.info(
      'VisaDocumentManagerService',
      'Removing cached document: $documentId',
    );
  }
}
