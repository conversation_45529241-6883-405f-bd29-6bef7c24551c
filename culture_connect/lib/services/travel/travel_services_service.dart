import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/travel_services_provider.dart';
import 'package:culture_connect/services/travel/travel_services_mock_data.dart';

/// Service for managing travel services
class TravelServicesService {
  // Singleton instance
  static final TravelServicesService _instance =
      TravelServicesService._internal();
  factory TravelServicesService() => _instance;
  TravelServicesService._internal();

  // In-memory storage for travel services (replace with API calls in production)
  final List<TravelService> _travelServices = [];
  final List<PriceAlert> _priceAlerts = [];
  final List<PriceComparison> _priceComparisons = [];

  /// Get the travel services list
  List<TravelService> get travelServices => _travelServices;

  /// Get the price alerts list
  List<PriceAlert> get priceAlerts => _priceAlerts;

  /// Get the price comparisons list
  List<PriceComparison> get priceComparisons => _priceComparisons;

  /// Initialize the service with mock data
  Future<void> initialize() async {
    if (_travelServices.isNotEmpty) return;

    // Generate mock data
    await _generateMockData();
  }

  /// Get all travel services
  Future<List<TravelService>> getAllTravelServices() async {
    await initialize();
    return _travelServices;
  }

  /// Get featured travel services
  Future<List<TravelService>> getFeaturedTravelServices() async {
    await initialize();
    return _travelServices.where((service) => service.isFeatured).toList();
  }

  /// Get travel services on sale
  Future<List<TravelService>> getOnSaleTravelServices() async {
    await initialize();
    return _travelServices.where((service) => service.isOnSale).toList();
  }

  /// Get car rentals
  Future<List<CarRental>> getCarRentals() async {
    await initialize();
    return _travelServices.whereType<CarRental>().toList();
  }

  /// Get private security services
  Future<List<PrivateSecurity>> getPrivateSecurityServices() async {
    await initialize();
    return _travelServices.whereType<PrivateSecurity>().toList();
  }

  /// Get hotels
  Future<List<Hotel>> getHotels() async {
    await initialize();
    return _travelServices.whereType<Hotel>().toList();
  }

  /// Get restaurants
  Future<List<Restaurant>> getRestaurants() async {
    await initialize();
    return _travelServices.whereType<Restaurant>().toList();
  }

  /// Get flights
  Future<List<Flight>> getFlights() async {
    await initialize();
    return _travelServices.whereType<Flight>().toList();
  }

  /// Get cruises
  Future<List<Cruise>> getCruises() async {
    await initialize();
    return _travelServices.whereType<Cruise>().toList();
  }

  /// Get loyalty programs
  Future<List<LoyaltyProgram>> getLoyaltyPrograms() async {
    // In a real app, this would come from an API
    // For now, return mock data
    return [
      LoyaltyProgram(
        id: 'lp1',
        name: 'CultureConnect Rewards',
        description:
            'Earn points for every booking and redeem for travel services.',
        type: LoyaltyProgramType.travel,
        companyName: 'CultureConnect',
        companyLogoUrl: 'https://example.com/logos/cultureconnect.png',
        currentTier: LoyaltyProgramTier.silver,
        currentPoints: 2500,
        pointsForNextTier: 5000,
        nextTier: LoyaltyProgramTier.gold,
        pointsExpiryDate: DateTime.now().add(const Duration(days: 365)),
        pointsExpiryAmount: 500,
        membershipNumber: 'CC123456789',
        membershipStartDate: DateTime.now().subtract(const Duration(days: 365)),
        benefits: [
          const LoyaltyProgramBenefit(
            id: 'b1',
            name: 'Priority Booking',
            description: 'Get priority access to new experiences.',
            icon: Icons.star,
            tier: LoyaltyProgramTier.silver,
          ),
          const LoyaltyProgramBenefit(
            id: 'b2',
            name: 'Free Upgrades',
            description: 'Get free upgrades on travel services when available.',
            icon: Icons.upgrade,
            tier: LoyaltyProgramTier.gold,
          ),
          const LoyaltyProgramBenefit(
            id: 'b3',
            name: 'Exclusive Experiences',
            description:
                'Access to exclusive experiences not available to the public.',
            icon: Icons.lock,
            tier: LoyaltyProgramTier.platinum,
          ),
        ],
        availableRewards: [
          const LoyaltyProgramReward(
            id: 'r1',
            name: 'Free Hotel Night',
            description: 'Redeem for a free night at select hotels.',
            pointsRequired: 5000,
            imageUrl: 'https://example.com/rewards/hotel.png',
            isAvailable: true,
          ),
          const LoyaltyProgramReward(
            id: 'r2',
            name: 'Airport Lounge Access',
            description: 'Redeem for a one-time access to airport lounges.',
            pointsRequired: 2000,
            imageUrl: 'https://example.com/rewards/lounge.png',
            isAvailable: true,
          ),
          const LoyaltyProgramReward(
            id: 'r3',
            name: 'Car Rental Upgrade',
            description: 'Redeem for a free upgrade on your next car rental.',
            pointsRequired: 1500,
            imageUrl: 'https://example.com/rewards/car.png',
            isAvailable: true,
          ),
        ],
        recentActivities: [
          LoyaltyProgramActivity(
            id: 'a1',
            type: LoyaltyProgramActivityType.earn,
            description: 'Booking: Cultural Walking Tour',
            points: 500,
            date: DateTime.now().subtract(const Duration(days: 30)),
            referenceNumber: 'B123456',
          ),
          LoyaltyProgramActivity(
            id: 'a2',
            type: LoyaltyProgramActivityType.redeem,
            description: 'Reward: Airport Lounge Access',
            points: 2000,
            date: DateTime.now().subtract(const Duration(days: 60)),
            referenceNumber: 'R789012',
          ),
          LoyaltyProgramActivity(
            id: 'a3',
            type: LoyaltyProgramActivityType.tierChange,
            description: 'Tier Change: Silver',
            points: 0,
            date: DateTime.now().subtract(const Duration(days: 90)),
          ),
        ],
      ),
    ];
  }

  /// Get a travel service by ID
  Future<TravelService?> getTravelServiceById(String id) async {
    await initialize();
    try {
      return _travelServices.firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get a car rental by ID
  Future<CarRental?> getCarRentalById(String id) async {
    await initialize();
    try {
      return _travelServices
          .whereType<CarRental>()
          .firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get a private security service by ID
  Future<PrivateSecurity?> getPrivateSecurityById(String id) async {
    await initialize();
    try {
      return _travelServices
          .whereType<PrivateSecurity>()
          .firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get a hotel by ID
  Future<Hotel?> getHotelById(String id) async {
    await initialize();
    try {
      return _travelServices
          .whereType<Hotel>()
          .firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get a restaurant by ID
  Future<Restaurant?> getRestaurantById(String id) async {
    await initialize();
    try {
      return _travelServices
          .whereType<Restaurant>()
          .firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get a flight by ID
  Future<Flight?> getFlightById(String id) async {
    await initialize();
    try {
      return _travelServices
          .whereType<Flight>()
          .firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get a cruise by ID
  Future<Cruise?> getCruiseById(String id) async {
    await initialize();
    try {
      return _travelServices
          .whereType<Cruise>()
          .firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get a loyalty program by ID
  Future<LoyaltyProgram?> getLoyaltyProgramById(String id) async {
    final loyaltyPrograms = await getLoyaltyPrograms();
    try {
      return loyaltyPrograms.firstWhere((program) => program.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get travel services by type
  Future<List<TravelService>> getTravelServicesByType(
      TravelServiceType type) async {
    await initialize();
    switch (type) {
      case TravelServiceType.carRental:
        return getCarRentals();
      case TravelServiceType.privateSecurity:
        return getPrivateSecurityServices();
      case TravelServiceType.hotel:
        return getHotels();
      case TravelServiceType.restaurant:
        return getRestaurants();
      case TravelServiceType.flight:
        return getFlights();
      case TravelServiceType.cruise:
        return getCruises();
      case TravelServiceType.insurance:
        // Insurance services are handled by the insurance service
        // Return empty list as this service doesn't manage insurance
        return <TravelService>[];
    }
  }

  /// Get price alerts
  Future<List<PriceAlert>> getPriceAlerts() async {
    await initialize();
    return _priceAlerts;
  }

  /// Get price comparisons for a travel service
  Future<List<PriceComparison>> getPriceComparisons(
      String travelServiceId) async {
    await initialize();
    return _priceComparisons
        .where((comparison) => comparison.travelServiceId == travelServiceId)
        .toList();
  }

  /// Create a price alert
  Future<PriceAlert> createPriceAlert({
    required String travelServiceId,
    required double targetPrice,
  }) async {
    await initialize();

    // Get the travel service
    final travelService = await getTravelServiceById(travelServiceId);
    if (travelService == null) {
      throw Exception('Travel service not found');
    }

    // Create the price alert
    final priceAlert = PriceAlert(
      id: 'pa${_priceAlerts.length + 1}',
      travelServiceId: travelServiceId,
      travelServiceType: _getTravelServiceType(travelService),
      travelServiceName: travelService.name,
      currentPrice: travelService.price,
      targetPrice: targetPrice,
      currency: travelService.currency,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Add to in-memory storage
    _priceAlerts.add(priceAlert);

    return priceAlert;
  }

  /// Delete a price alert
  Future<void> deletePriceAlert(String priceAlertId) async {
    await initialize();

    // Remove from in-memory storage
    _priceAlerts.removeWhere((alert) => alert.id == priceAlertId);
  }

  /// Get the travel service type for a travel service
  TravelServiceType _getTravelServiceType(TravelService travelService) {
    if (travelService is CarRental) {
      return TravelServiceType.carRental;
    } else if (travelService is PrivateSecurity) {
      return TravelServiceType.privateSecurity;
    } else if (travelService is Hotel) {
      return TravelServiceType.hotel;
    } else if (travelService is Restaurant) {
      return TravelServiceType.restaurant;
    } else if (travelService is Flight) {
      return TravelServiceType.flight;
    } else if (travelService is Cruise) {
      return TravelServiceType.cruise;
    } else {
      throw Exception('Unknown travel service type');
    }
  }

  /// Generate mock data for testing
  Future<void> _generateMockData() async {
    // Use the extension method from travel_services_mock_data.dart
    await generateMockData();
  }
}
